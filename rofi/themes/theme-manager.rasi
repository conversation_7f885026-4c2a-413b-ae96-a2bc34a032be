/* Theme Manager - Simple Rofi Theme */

configuration {
    font: "JetBrains Mono 12";
    show-icons: true;
    display-drun: "";
    disable-history: false;
    click-to-exit: true;
}

* {
    background:     #1e1e2e;
    background-alt: #313244;
    foreground:     #cdd6f4;
    foreground-alt: #bac2de;
    accent:         #89b4fa;
    
    background-color: transparent;
    text-color: @foreground;
}

window {
    transparency: "real";
    background-color: @background;
    border: 2px solid;
    border-color: @accent;
    border-radius: 12px;
    width: 500px;
    location: center;
    anchor: center;
}

prompt {
    enabled: true;
    padding: 8px 12px;
    background-color: @accent;
    text-color: @background;
    border-radius: 8px;
    margin: 0px 8px 0px 0px;
}

entry {
    placeholder: "Search themes...";
    placeholder-color: @foreground-alt;
    padding: 8px;
    border-radius: 4px;
    background-color: @background-alt;
}

inputbar {
    children: [ prompt, entry ];
    spacing: 8px;
    padding: 16px;
    background-color: @background;
}

listview {
    columns: 1;
    lines: 8;
    cycle: true;
    dynamic: true;
    scrollbar: false;
    spacing: 4px;
    padding: 8px 16px;
    background-color: @background;
}

element {
    border-radius: 8px;
    padding: 12px;
    background-color: transparent;
    text-color: @foreground;
}

element selected {
    background-color: @accent;
    text-color: @background;
}

element alternate {
    background-color: @background-alt;
}

message {
    padding: 16px;
    background-color: @background-alt;
    border-radius: 8px;
    margin: 8px 16px;
}

textbox {
    padding: 8px;
    background-color: transparent;
    text-color: @foreground;
}
