#!/bin/bash
# Theme Manager GUI using existing rofi themes

# Use existing rofi launcher theme
ROFI_THEME="$HOME/.config/hypr/rofi/themes/launchers/type-1/style-1.rasi"

show_main_menu() {
    local options="🎨 GTK/Qt Themes
🖼️ Icon Themes  
🖱️ Cursor Themes
🔄 Apply Unified Theme
📋 Current Status
⚙️ Quick Presets
❌ Exit"

    echo "$options" | rofi -dmenu -i \
        -p "Theme Manager" \
        -theme "$ROFI_THEME" \
        -mesg "Select theme category to manage"
}

show_gtk_themes() {
    # List available GTK themes from /usr/share/themes and ~/.themes
    local themes=$(find /usr/share/themes ~/.themes -maxdepth 1 -type d -name "*" 2>/dev/null | \
        grep -E "(Arc|Adwaita|Sweet|Dracula|Nordic|Breeze)" | \
        xargs -I {} basename {} | sort -u)
    
    local selected=$(echo "$themes" | rofi -dmenu -i \
        -p "Select GTK Theme" \
        -theme "$ROFI_THEME")
    
    if [[ -n "$selected" ]]; then
        gsettings set org.gnome.desktop.interface gtk-theme "$selected"
        gsettings set org.gnome.desktop.wm.preferences theme "$selected"
        notify-send "Theme Applied" "GTK Theme: $selected" -i preferences-desktop-theme
    fi
}

show_icon_themes() {
    # List available icon themes
    local themes=$(find /usr/share/icons ~/.icons -maxdepth 1 -type d -name "*" 2>/dev/null | \
        grep -E "(Papirus|Adwaita|Arc|Sweet|Dracula|Nordic|Breeze|candy)" | \
        xargs -I {} basename {} | sort -u)
    
    local selected=$(echo "$themes" | rofi -dmenu -i \
        -p "Select Icon Theme" \
        -theme "$ROFI_THEME")
    
    if [[ -n "$selected" ]]; then
        gsettings set org.gnome.desktop.interface icon-theme "$selected"
        notify-send "Icons Applied" "Icon Theme: $selected" -i folder-pictures
    fi
}

show_cursor_themes() {
    # List available cursor themes
    local themes=$(find /usr/share/icons ~/.icons -maxdepth 1 -type d -name "*" 2>/dev/null | \
        xargs -I {} find {} -name "cursors" -type d 2>/dev/null | \
        xargs -I {} dirname {} | xargs -I {} basename {} | sort -u)
    
    local selected=$(echo "$themes" | rofi -dmenu -i \
        -p "Select Cursor Theme" \
        -theme "$ROFI_THEME")
    
    if [[ -n "$selected" ]]; then
        gsettings set org.gnome.desktop.interface cursor-theme "$selected"
        notify-send "Cursor Applied" "Cursor Theme: $selected" -i input-mouse
    fi
}

show_presets() {
    local presets="🌙 Dark Preset
☀️ Light Preset  
🎯 Arc Preset
🔵 Breeze Preset
🟣 Dracula Preset"

    local selected=$(echo "$presets" | rofi -dmenu -i \
        -p "Select Preset" \
        -theme "$ROFI_THEME")

    case "$selected" in
        *"Dark Preset"*)
            apply_preset "Sweet-Dark-v40" "Papirus-Dark" "Sweet-cursors"
            ;;
        *"Light Preset"*)
            apply_preset "Adwaita" "Adwaita" "Adwaita"
            ;;
        *"Arc Preset"*)
            apply_preset "Arc-Dark" "Papirus-Dark" "Sweet-cursors"
            ;;
        *"Breeze Preset"*)
            apply_preset "Breeze-Dark" "breeze-dark" "breeze_cursors"
            ;;
        *"Dracula Preset"*)
            apply_preset "Dracula" "Papirus-Dark" "Sweet-cursors"
            ;;
    esac
}

apply_preset() {
    local gtk_theme="$1"
    local icon_theme="$2" 
    local cursor_theme="$3"
    
    # Apply all themes
    gsettings set org.gnome.desktop.interface gtk-theme "$gtk_theme"
    gsettings set org.gnome.desktop.wm.preferences theme "$gtk_theme"
    gsettings set org.gnome.desktop.interface icon-theme "$icon_theme"
    gsettings set org.gnome.desktop.interface cursor-theme "$cursor_theme"
    
    # Apply to Qt apps
    echo "export QT_STYLE_OVERRIDE=$gtk_theme" > ~/.config/qt5ct/qt5ct.conf
    
    notify-send "Preset Applied" "GTK: $gtk_theme\nIcons: $icon_theme\nCursor: $cursor_theme" -i preferences-desktop-theme
}

show_current_status() {
    local gtk_theme=$(gsettings get org.gnome.desktop.interface gtk-theme | tr -d "'")
    local icon_theme=$(gsettings get org.gnome.desktop.interface icon-theme | tr -d "'")
    local cursor_theme=$(gsettings get org.gnome.desktop.interface cursor-theme | tr -d "'")
    
    local status="Current Configuration:

🎨 GTK Theme: $gtk_theme
🖼️ Icon Theme: $icon_theme  
🖱️ Cursor Theme: $cursor_theme

Press ESC to return"

    echo "$status" | rofi -dmenu -i \
        -p "Current Status" \
        -theme "$ROFI_THEME" \
        -mesg "Theme Status"
}

# Main loop
while true; do
    choice=$(show_main_menu)
    
    case "$choice" in
        *"GTK/Qt Themes"*)
            show_gtk_themes
            ;;
        *"Icon Themes"*)
            show_icon_themes
            ;;
        *"Cursor Themes"*)
            show_cursor_themes
            ;;
        *"Apply Unified"*)
            if [[ -f ~/.config/hypr/scripts/system/theme-unifier.sh ]]; then
                ~/.config/hypr/scripts/system/theme-unifier.sh apply
                notify-send "Themes Unified" "All themes synchronized" -i preferences-desktop-theme
            else
                notify-send "Error" "theme-unifier.sh not found" -i dialog-error
            fi
            ;;
        *"Current Status"*)
            show_current_status
            ;;
        *"Quick Presets"*)
            show_presets
            ;;
        *"Exit"*|"")
            break
            ;;
    esac
done
